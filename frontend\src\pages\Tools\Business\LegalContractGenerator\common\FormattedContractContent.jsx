import React, { memo } from 'react';
import cn from 'classnames';

/**
 * FormattedContractContent - Professional typography component for legal contracts
 * Based on Business Plan Generator's FormattedContent patterns
 */
const FormattedContractContent = memo(({ contractContent, language, contractType }) => {
    // Return null if contract content isn't available yet
    if (!contractContent) return null;

    const rtlLanguages = ['Arabic', 'Hebrew', 'Persian', 'Urdu', 'Yiddish'];
    const isRtl = rtlLanguages.includes(language);
    const lines = contractContent.split('\n').filter(line => line.trim() !== '');

    // Get contract type specific colors
    const getContractColors = (contractType) => {
        switch (contractType) {
            case 'service':
                return {
                    primary: 'from-blue-400 to-cyan-400',
                    secondary: 'text-blue-300',
                    accent: 'text-cyan-300'
                };
            case 'partnership':
                return {
                    primary: 'from-purple-400 to-pink-400',
                    secondary: 'text-purple-300',
                    accent: 'text-pink-300'
                };
            default:
                return {
                    primary: 'from-sky-400 to-purple-400',
                    secondary: 'text-sky-300',
                    accent: 'text-purple-300'
                };
        }
    };

    const colors = getContractColors(contractType);

    return (
        // Set the text direction for the entire container based on the language
        <div dir={isRtl ? 'rtl' : 'ltr'} className={cn({ 'text-right': isRtl })}>
            {lines.map((line, index) => {
                // Main contract title (~H~)
                if (line.startsWith('~H~')) {
                    return (
                        <h1 
                            key={index} 
                            className={`text-4xl font-bold bg-gradient-to-r ${colors.primary} bg-clip-text text-transparent mb-8 pb-3 border-b-2 border-slate-700/50 text-center`}
                        >
                            {line.substring(3).trim()}
                        </h1>
                    );
                }
                
                // Section headers (~S~)
                if (line.startsWith('~S~')) {
                    return (
                        <h2 
                            key={index} 
                            className={`text-2xl font-bold ${colors.secondary} mt-8 mb-4 pb-2 border-b border-slate-600/30`}
                        >
                            {line.substring(3).trim()}
                        </h2>
                    );
                }
                
                // Sub-section headers (~H_SUB~)
                if (line.startsWith('~H_SUB~')) {
                    return (
                        <h3 
                            key={index} 
                            className={`text-xl font-semibold ${colors.accent} mt-6 mb-3`}
                        >
                            {line.substring(7).trim()}
                        </h3>
                    );
                }
                
                // Contract sections with numbering (~SECTION~)
                if (line.startsWith('~SECTION~')) {
                    return (
                        <div key={index} className="mt-8 mb-4">
                            <h3 className={`text-xl font-bold ${colors.secondary} mb-2 flex items-center`}>
                                <span className={`w-8 h-8 bg-gradient-to-r ${colors.primary} rounded-full flex items-center justify-center text-white text-sm font-bold mr-3`}>
                                    {index + 1}
                                </span>
                                {line.substring(9).trim()}
                            </h3>
                            <div className={`h-0.5 bg-gradient-to-r ${colors.primary} opacity-30 ml-11`}></div>
                        </div>
                    );
                }
                
                // Important clauses (~CLAUSE~)
                if (line.startsWith('~CLAUSE~')) {
                    return (
                        <div key={index} className="my-6 p-4 bg-gradient-to-r from-slate-800/50 to-slate-700/30 border-l-4 border-blue-400 rounded-r-lg">
                            <p className="text-slate-200 font-medium leading-relaxed">
                                {line.substring(8).trim()}
                            </p>
                        </div>
                    );
                }
                
                // List items (~L~)
                if (line.startsWith('~L~')) {
                    return (
                        <div key={index} className={cn('flex items-start mb-3', { 'flex-row-reverse': isRtl })}>
                            <div className="shrink-0">
                                <span className={cn(`w-2 h-2 bg-gradient-to-r ${colors.primary} rounded-full inline-block mt-2`, { 'ml-3': !isRtl, 'mr-3': isRtl })} />
                            </div>
                            <p className="flex-1 text-slate-300 leading-relaxed">
                                {line.substring(3).trim()}
                            </p>
                        </div>
                    );
                }
                
                // Numbered list items (~N~)
                if (line.startsWith('~N~')) {
                    const match = line.match(/~N~(\d+)~(.+)/);
                    if (match) {
                        const [, number, text] = match;
                        return (
                            <div key={index} className={cn('flex items-start mb-3', { 'flex-row-reverse': isRtl })}>
                                <div className="shrink-0">
                                    <span className={cn(`w-6 h-6 bg-gradient-to-r ${colors.primary} rounded-full flex items-center justify-center text-white text-xs font-bold`, { 'ml-3': !isRtl, 'mr-3': isRtl })}>
                                        {number}
                                    </span>
                                </div>
                                <p className="flex-1 text-slate-300 leading-relaxed">
                                    {text.trim()}
                                </p>
                            </div>
                        );
                    }
                }
                
                // Paragraphs (~P~)
                if (line.startsWith('~P~')) {
                    return (
                        <p key={index} className="text-slate-300 leading-relaxed mb-4 text-justify">
                            {line.substring(3).trim()}
                        </p>
                    );
                }
                
                // Signature blocks (~SIGNATURE~)
                if (line.startsWith('~SIGNATURE~')) {
                    const signatureText = line.substring(11).trim();
                    return (
                        <div key={index} className="mt-12 mb-8 p-6 bg-slate-800/30 border border-slate-600/30 rounded-xl">
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                                <div className="space-y-4">
                                    <div className="border-b border-slate-500 pb-2">
                                        <p className="text-slate-400 text-sm mb-1">Signature</p>
                                        <div className="h-8"></div>
                                    </div>
                                    <div>
                                        <p className="text-slate-400 text-sm">Date: _______________</p>
                                    </div>
                                </div>
                                <div className="space-y-4">
                                    <div className="border-b border-slate-500 pb-2">
                                        <p className="text-slate-400 text-sm mb-1">Print Name</p>
                                        <div className="h-8"></div>
                                    </div>
                                    <div>
                                        <p className="text-slate-400 text-sm">Title: _______________</p>
                                    </div>
                                </div>
                            </div>
                            {signatureText && (
                                <p className="text-slate-300 text-center mt-4 font-medium">
                                    {signatureText}
                                </p>
                            )}
                        </div>
                    );
                }
                
                // Witness blocks (~WITNESS~)
                if (line.startsWith('~WITNESS~')) {
                    return (
                        <div key={index} className="mt-8 mb-6 p-4 bg-slate-800/20 border border-slate-600/20 rounded-lg">
                            <h4 className="text-slate-300 font-semibold mb-4">Witness</h4>
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <div className="border-b border-slate-500 pb-2">
                                    <p className="text-slate-400 text-sm mb-1">Witness Signature</p>
                                    <div className="h-6"></div>
                                </div>
                                <div className="border-b border-slate-500 pb-2">
                                    <p className="text-slate-400 text-sm mb-1">Date</p>
                                    <div className="h-6"></div>
                                </div>
                            </div>
                        </div>
                    );
                }
                
                // Notary blocks (~NOTARY~)
                if (line.startsWith('~NOTARY~')) {
                    return (
                        <div key={index} className="mt-8 mb-6 p-6 bg-gradient-to-r from-amber-900/20 to-yellow-900/20 border border-amber-500/30 rounded-xl">
                            <h4 className="text-amber-300 font-bold mb-4 flex items-center">
                                <span className="w-6 h-6 bg-amber-500 rounded-full flex items-center justify-center text-amber-900 text-sm font-bold mr-2">N</span>
                                Notarization
                            </h4>
                            <div className="space-y-4">
                                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                    <div className="border-b border-amber-400/30 pb-2">
                                        <p className="text-amber-200 text-sm mb-1">Notary Signature</p>
                                        <div className="h-8"></div>
                                    </div>
                                    <div className="border-b border-amber-400/30 pb-2">
                                        <p className="text-amber-200 text-sm mb-1">Date</p>
                                        <div className="h-8"></div>
                                    </div>
                                </div>
                                <div className="border-b border-amber-400/30 pb-2">
                                    <p className="text-amber-200 text-sm mb-1">Notary Seal</p>
                                    <div className="h-12 border-2 border-dashed border-amber-400/30 rounded-lg flex items-center justify-center">
                                        <span className="text-amber-400/50 text-sm">Affix Notary Seal Here</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    );
                }
                
                // Default paragraph formatting for untagged content
                if (line.trim()) {
                    return (
                        <p key={index} className="text-slate-400 leading-relaxed mb-3">
                            {line}
                        </p>
                    );
                }
                
                return null;
            })}
        </div>
    );
});

FormattedContractContent.displayName = 'FormattedContractContent';

export default FormattedContractContent;
